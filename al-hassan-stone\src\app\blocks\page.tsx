'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Package, 
  Plus, 
  Search, 
  Calendar,
  Ruler,
  Weight,
  ArrowLeft,
  Edit,
  Eye,
  Truck
} from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

interface Block {
  id: string
  truck_id: string
  block_number: string
  stone_type: string
  dimensions_length: number
  dimensions_width: number
  dimensions_height: number
  weight: number
  quality_grade: 'A' | 'B' | 'C'
  location_in_yard: string
  status: 'received' | 'in_cutting' | 'cut' | 'sold'
  notes?: string
  created_at: string
  updated_at: string
  trucks?: {
    truck_number: string
    driver_name: string
  }
}

interface Truck {
  id: string
  truck_number: string
  driver_name: string
}

export default function BlocksPage() {
  const [blocks, setBlocks] = useState<Block[]>([])
  const [trucks, setTrucks] = useState<Truck[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [newBlock, setNewBlock] = useState({
    truck_id: '',
    block_number: '',
    stone_type: '',
    dimensions_length: '',
    dimensions_width: '',
    dimensions_height: '',
    weight: '',
    quality_grade: 'A' as 'A' | 'B' | 'C',
    location_in_yard: '',
    notes: ''
  })
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/login')
        return
      }
      await loadData()
    }
    checkAuth()
  }, [router])

  const loadData = async () => {
    try {
      // Load blocks with truck information
      const { data: blocksData, error: blocksError } = await supabase
        .from('blocks')
        .select(`
          *,
          trucks (
            truck_number,
            driver_name
          )
        `)
        .order('created_at', { ascending: false })

      if (blocksError) throw blocksError

      // Load trucks for the dropdown
      const { data: trucksData, error: trucksError } = await supabase
        .from('trucks')
        .select('id, truck_number, driver_name')
        .eq('status', 'arrived')
        .order('truck_number')

      if (trucksError) throw trucksError

      setBlocks(blocksData || [])
      setTrucks(trucksData || [])
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddBlock = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const { data, error } = await supabase
        .from('blocks')
        .insert([{
          ...newBlock,
          dimensions_length: parseFloat(newBlock.dimensions_length),
          dimensions_width: parseFloat(newBlock.dimensions_width),
          dimensions_height: parseFloat(newBlock.dimensions_height),
          weight: parseFloat(newBlock.weight),
          status: 'received'
        }])
        .select(`
          *,
          trucks (
            truck_number,
            driver_name
          )
        `)

      if (error) throw error

      setBlocks([data[0], ...blocks])
      setNewBlock({
        truck_id: '',
        block_number: '',
        stone_type: '',
        dimensions_length: '',
        dimensions_width: '',
        dimensions_height: '',
        weight: '',
        quality_grade: 'A',
        location_in_yard: '',
        notes: ''
      })
      setShowAddForm(false)
    } catch (error) {
      console.error('Error adding block:', error)
      alert('حدث خطأ في إضافة البلوك')
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'received': return 'مستلم'
      case 'in_cutting': return 'قيد النشر'
      case 'cut': return 'تم النشر'
      case 'sold': return 'مباع'
      default: return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'received': return 'bg-blue-100 text-blue-800'
      case 'in_cutting': return 'bg-yellow-100 text-yellow-800'
      case 'cut': return 'bg-green-100 text-green-800'
      case 'sold': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getQualityColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'bg-green-100 text-green-800'
      case 'B': return 'bg-yellow-100 text-yellow-800'
      case 'C': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const calculateVolume = (length: number, width: number, height: number) => {
    return (length * width * height).toFixed(2)
  }

  const filteredBlocks = blocks.filter(block =>
    block.block_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    block.stone_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    block.trucks?.truck_number.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/dashboard')}
                className="ml-4"
              >
                <ArrowLeft className="w-4 h-4 ml-2" />
                العودة
              </Button>
              <h1 className="text-xl font-semibold text-gray-900 flex items-center">
                <Package className="w-6 h-6 ml-2" />
                إدارة البلوكات
              </h1>
            </div>
            <Button onClick={() => setShowAddForm(true)}>
              <Plus className="w-4 h-4 ml-2" />
              إضافة بلوك جديد
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Search */}
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="البحث برقم البلوك أو نوع الحجر أو رقم الجرار..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </div>

          {/* Add Block Form */}
          {showAddForm && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>إضافة بلوك جديد</CardTitle>
                <CardDescription>
                  أدخل بيانات البلوك الجديد
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleAddBlock} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="truck_id">الجرار</Label>
                      <select
                        id="truck_id"
                        value={newBlock.truck_id}
                        onChange={(e) => setNewBlock({...newBlock, truck_id: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="">اختر الجرار</option>
                        {trucks.map((truck) => (
                          <option key={truck.id} value={truck.id}>
                            {truck.truck_number} - {truck.driver_name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="block_number">رقم البلوك</Label>
                      <Input
                        id="block_number"
                        value={newBlock.block_number}
                        onChange={(e) => setNewBlock({...newBlock, block_number: e.target.value})}
                        placeholder="أدخل رقم البلوك"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="stone_type">نوع الحجر</Label>
                      <Input
                        id="stone_type"
                        value={newBlock.stone_type}
                        onChange={(e) => setNewBlock({...newBlock, stone_type: e.target.value})}
                        placeholder="مثال: جرانيت أحمر أسوان"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="dimensions_length">الطول (متر)</Label>
                      <Input
                        id="dimensions_length"
                        type="number"
                        step="0.01"
                        value={newBlock.dimensions_length}
                        onChange={(e) => setNewBlock({...newBlock, dimensions_length: e.target.value})}
                        placeholder="الطول"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="dimensions_width">العرض (متر)</Label>
                      <Input
                        id="dimensions_width"
                        type="number"
                        step="0.01"
                        value={newBlock.dimensions_width}
                        onChange={(e) => setNewBlock({...newBlock, dimensions_width: e.target.value})}
                        placeholder="العرض"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="dimensions_height">الارتفاع (متر)</Label>
                      <Input
                        id="dimensions_height"
                        type="number"
                        step="0.01"
                        value={newBlock.dimensions_height}
                        onChange={(e) => setNewBlock({...newBlock, dimensions_height: e.target.value})}
                        placeholder="الارتفاع"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="weight">الوزن (طن)</Label>
                      <Input
                        id="weight"
                        type="number"
                        step="0.01"
                        value={newBlock.weight}
                        onChange={(e) => setNewBlock({...newBlock, weight: e.target.value})}
                        placeholder="الوزن"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="quality_grade">درجة الجودة</Label>
                      <select
                        id="quality_grade"
                        value={newBlock.quality_grade}
                        onChange={(e) => setNewBlock({...newBlock, quality_grade: e.target.value as 'A' | 'B' | 'C'})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="A">A - ممتاز</option>
                        <option value="B">B - جيد</option>
                        <option value="C">C - مقبول</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="location_in_yard">موقع في الساحة</Label>
                      <Input
                        id="location_in_yard"
                        value={newBlock.location_in_yard}
                        onChange={(e) => setNewBlock({...newBlock, location_in_yard: e.target.value})}
                        placeholder="مثال: القطاع A - الصف 1"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notes">ملاحظات</Label>
                    <Input
                      id="notes"
                      value={newBlock.notes}
                      onChange={(e) => setNewBlock({...newBlock, notes: e.target.value})}
                      placeholder="ملاحظات إضافية (اختياري)"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button type="submit">
                      حفظ البلوك
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                      إلغاء
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Blocks Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredBlocks.map((block) => (
              <Card key={block.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">
                      بلوك رقم {block.block_number}
                    </CardTitle>
                    <div className="flex flex-col gap-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(block.status)}`}>
                        {getStatusText(block.status)}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getQualityColor(block.quality_grade)}`}>
                        جودة {block.quality_grade}
                      </span>
                    </div>
                  </div>
                  <CardDescription>
                    {block.stone_type}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <Truck className="w-4 h-4 ml-2 text-gray-500" />
                      <span>جرار: {block.trucks?.truck_number}</span>
                    </div>
                    <div className="flex items-center">
                      <Ruler className="w-4 h-4 ml-2 text-gray-500" />
                      <span>
                        الأبعاد: {block.dimensions_length} × {block.dimensions_width} × {block.dimensions_height} م
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Weight className="w-4 h-4 ml-2 text-gray-500" />
                      <span>الوزن: {block.weight} طن</span>
                    </div>
                    <div className="text-gray-600">
                      <strong>الحجم:</strong> {calculateVolume(block.dimensions_length, block.dimensions_width, block.dimensions_height)} م³
                    </div>
                    <div className="text-gray-600">
                      <strong>الموقع:</strong> {block.location_in_yard}
                    </div>
                    {block.notes && (
                      <div className="text-gray-600">
                        <strong>ملاحظات:</strong> {block.notes}
                      </div>
                    )}
                  </div>
                  <div className="flex gap-2 mt-4">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => router.push(`/blocks/${block.id}`)}
                    >
                      <Eye className="w-4 h-4 ml-1" />
                      عرض
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => router.push(`/blocks/${block.id}/edit`)}
                    >
                      <Edit className="w-4 h-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredBlocks.length === 0 && (
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                لا توجد بلوكات
              </h3>
              <p className="text-gray-600 mb-4">
                {searchTerm ? 'لم يتم العثور على بلوكات تطابق البحث' : 'لم يتم إضافة أي بلوكات بعد'}
              </p>
              {!searchTerm && (
                <Button onClick={() => setShowAddForm(true)}>
                  <Plus className="w-4 h-4 ml-2" />
                  إضافة أول بلوك
                </Button>
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
