'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Scissors, 
  Plus, 
  Search, 
  Calendar,
  Ruler,
  Package,
  ArrowLeft,
  Edit,
  Eye
} from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

interface Cut {
  id: string
  block_id: string
  cut_number: string
  thickness: number
  length: number
  width: number
  area: number
  finish_type: string
  quality_grade: 'A' | 'B' | 'C'
  status: 'cut' | 'polished' | 'in_stock' | 'sold'
  cutting_date: string
  cutting_cost: number
  polishing_cost?: number
  notes?: string
  created_at: string
  updated_at: string
  blocks?: {
    block_number: string
    stone_type: string
  }
}

interface Block {
  id: string
  block_number: string
  stone_type: string
  status: string
}

export default function CutsPage() {
  const [cuts, setCuts] = useState<Cut[]>([])
  const [blocks, setBlocks] = useState<Block[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [newCut, setNewCut] = useState({
    block_id: '',
    cut_number: '',
    thickness: '',
    length: '',
    width: '',
    finish_type: '',
    quality_grade: 'A' as 'A' | 'B' | 'C',
    cutting_date: new Date().toISOString().split('T')[0],
    cutting_cost: '',
    polishing_cost: '',
    notes: ''
  })
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/login')
        return
      }
      await loadData()
    }
    checkAuth()
  }, [router])

  const loadData = async () => {
    try {
      // Load cuts with block information
      const { data: cutsData, error: cutsError } = await supabase
        .from('cuts')
        .select(`
          *,
          blocks (
            block_number,
            stone_type
          )
        `)
        .order('created_at', { ascending: false })

      if (cutsError) throw cutsError

      // Load available blocks for cutting
      const { data: blocksData, error: blocksError } = await supabase
        .from('blocks')
        .select('id, block_number, stone_type, status')
        .in('status', ['received', 'in_cutting'])
        .order('block_number')

      if (blocksError) throw blocksError

      setCuts(cutsData || [])
      setBlocks(blocksData || [])
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddCut = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const length = parseFloat(newCut.length)
      const width = parseFloat(newCut.width)
      const area = length * width

      const { data, error } = await supabase
        .from('cuts')
        .insert([{
          ...newCut,
          thickness: parseFloat(newCut.thickness),
          length,
          width,
          area,
          cutting_cost: parseFloat(newCut.cutting_cost),
          polishing_cost: newCut.polishing_cost ? parseFloat(newCut.polishing_cost) : null,
          status: 'cut'
        }])
        .select(`
          *,
          blocks (
            block_number,
            stone_type
          )
        `)

      if (error) throw error

      // Update block status to in_cutting
      await supabase
        .from('blocks')
        .update({ status: 'in_cutting' })
        .eq('id', newCut.block_id)

      setCuts([data[0], ...cuts])
      setNewCut({
        block_id: '',
        cut_number: '',
        thickness: '',
        length: '',
        width: '',
        finish_type: '',
        quality_grade: 'A',
        cutting_date: new Date().toISOString().split('T')[0],
        cutting_cost: '',
        polishing_cost: '',
        notes: ''
      })
      setShowAddForm(false)
      await loadData() // Reload to update blocks list
    } catch (error) {
      console.error('Error adding cut:', error)
      alert('حدث خطأ في إضافة الشريحة')
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'cut': return 'تم النشر'
      case 'polished': return 'تم التلميع'
      case 'in_stock': return 'في المخزن'
      case 'sold': return 'مباع'
      default: return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'cut': return 'bg-blue-100 text-blue-800'
      case 'polished': return 'bg-green-100 text-green-800'
      case 'in_stock': return 'bg-yellow-100 text-yellow-800'
      case 'sold': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getQualityColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'bg-green-100 text-green-800'
      case 'B': return 'bg-yellow-100 text-yellow-800'
      case 'C': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredCuts = cuts.filter(cut =>
    cut.cut_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cut.finish_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cut.blocks?.block_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cut.blocks?.stone_type.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/dashboard')}
                className="ml-4"
              >
                <ArrowLeft className="w-4 h-4 ml-2" />
                العودة
              </Button>
              <h1 className="text-xl font-semibold text-gray-900 flex items-center">
                <Scissors className="w-6 h-6 ml-2" />
                إدارة النشر والشرائح
              </h1>
            </div>
            <Button onClick={() => setShowAddForm(true)}>
              <Plus className="w-4 h-4 ml-2" />
              إضافة شريحة جديدة
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Search */}
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="البحث برقم الشريحة أو نوع التشطيب أو رقم البلوك..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </div>

          {/* Add Cut Form */}
          {showAddForm && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>إضافة شريحة جديدة</CardTitle>
                <CardDescription>
                  أدخل بيانات الشريحة الجديدة من النشر
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleAddCut} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="block_id">البلوك</Label>
                      <select
                        id="block_id"
                        value={newCut.block_id}
                        onChange={(e) => setNewCut({...newCut, block_id: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="">اختر البلوك</option>
                        {blocks.map((block) => (
                          <option key={block.id} value={block.id}>
                            {block.block_number} - {block.stone_type}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cut_number">رقم الشريحة</Label>
                      <Input
                        id="cut_number"
                        value={newCut.cut_number}
                        onChange={(e) => setNewCut({...newCut, cut_number: e.target.value})}
                        placeholder="أدخل رقم الشريحة"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="thickness">السماكة (سم)</Label>
                      <Input
                        id="thickness"
                        type="number"
                        step="0.1"
                        value={newCut.thickness}
                        onChange={(e) => setNewCut({...newCut, thickness: e.target.value})}
                        placeholder="السماكة"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="length">الطول (متر)</Label>
                      <Input
                        id="length"
                        type="number"
                        step="0.01"
                        value={newCut.length}
                        onChange={(e) => setNewCut({...newCut, length: e.target.value})}
                        placeholder="الطول"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="width">العرض (متر)</Label>
                      <Input
                        id="width"
                        type="number"
                        step="0.01"
                        value={newCut.width}
                        onChange={(e) => setNewCut({...newCut, width: e.target.value})}
                        placeholder="العرض"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="finish_type">نوع التشطيب</Label>
                      <Input
                        id="finish_type"
                        value={newCut.finish_type}
                        onChange={(e) => setNewCut({...newCut, finish_type: e.target.value})}
                        placeholder="مثال: لامع، مطفي، منحوت"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="quality_grade">درجة الجودة</Label>
                      <select
                        id="quality_grade"
                        value={newCut.quality_grade}
                        onChange={(e) => setNewCut({...newCut, quality_grade: e.target.value as 'A' | 'B' | 'C'})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="A">A - ممتاز</option>
                        <option value="B">B - جيد</option>
                        <option value="C">C - مقبول</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cutting_date">تاريخ النشر</Label>
                      <Input
                        id="cutting_date"
                        type="date"
                        value={newCut.cutting_date}
                        onChange={(e) => setNewCut({...newCut, cutting_date: e.target.value})}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cutting_cost">تكلفة النشر (جنيه)</Label>
                      <Input
                        id="cutting_cost"
                        type="number"
                        step="0.01"
                        value={newCut.cutting_cost}
                        onChange={(e) => setNewCut({...newCut, cutting_cost: e.target.value})}
                        placeholder="تكلفة النشر"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="polishing_cost">تكلفة التلميع (جنيه)</Label>
                      <Input
                        id="polishing_cost"
                        type="number"
                        step="0.01"
                        value={newCut.polishing_cost}
                        onChange={(e) => setNewCut({...newCut, polishing_cost: e.target.value})}
                        placeholder="تكلفة التلميع (اختياري)"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notes">ملاحظات</Label>
                    <Input
                      id="notes"
                      value={newCut.notes}
                      onChange={(e) => setNewCut({...newCut, notes: e.target.value})}
                      placeholder="ملاحظات إضافية (اختياري)"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button type="submit">
                      حفظ الشريحة
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                      إلغاء
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Cuts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCuts.map((cut) => (
              <Card key={cut.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">
                      شريحة رقم {cut.cut_number}
                    </CardTitle>
                    <div className="flex flex-col gap-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(cut.status)}`}>
                        {getStatusText(cut.status)}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getQualityColor(cut.quality_grade)}`}>
                        جودة {cut.quality_grade}
                      </span>
                    </div>
                  </div>
                  <CardDescription>
                    {cut.blocks?.stone_type} - {cut.finish_type}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <Package className="w-4 h-4 ml-2 text-gray-500" />
                      <span>بلوك: {cut.blocks?.block_number}</span>
                    </div>
                    <div className="flex items-center">
                      <Ruler className="w-4 h-4 ml-2 text-gray-500" />
                      <span>
                        الأبعاد: {cut.length} × {cut.width} × {cut.thickness} سم
                      </span>
                    </div>
                    <div className="text-gray-600">
                      <strong>المساحة:</strong> {cut.area.toFixed(2)} م²
                    </div>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 ml-2 text-gray-500" />
                      <span>تاريخ النشر: {formatDate(cut.cutting_date)}</span>
                    </div>
                    <div className="text-gray-600">
                      <strong>تكلفة النشر:</strong> {formatCurrency(cut.cutting_cost)}
                    </div>
                    {cut.polishing_cost && (
                      <div className="text-gray-600">
                        <strong>تكلفة التلميع:</strong> {formatCurrency(cut.polishing_cost)}
                      </div>
                    )}
                    {cut.notes && (
                      <div className="text-gray-600">
                        <strong>ملاحظات:</strong> {cut.notes}
                      </div>
                    )}
                  </div>
                  <div className="flex gap-2 mt-4">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => router.push(`/cuts/${cut.id}`)}
                    >
                      <Eye className="w-4 h-4 ml-1" />
                      عرض
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => router.push(`/cuts/${cut.id}/edit`)}
                    >
                      <Edit className="w-4 h-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredCuts.length === 0 && (
            <div className="text-center py-12">
              <Scissors className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                لا توجد شرائح
              </h3>
              <p className="text-gray-600 mb-4">
                {searchTerm ? 'لم يتم العثور على شرائح تطابق البحث' : 'لم يتم إضافة أي شرائح بعد'}
              </p>
              {!searchTerm && (
                <Button onClick={() => setShowAddForm(true)}>
                  <Plus className="w-4 h-4 ml-2" />
                  إضافة أول شريحة
                </Button>
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
