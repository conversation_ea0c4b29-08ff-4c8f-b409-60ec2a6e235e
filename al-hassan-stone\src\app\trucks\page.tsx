'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Truck, 
  Plus, 
  Search, 
  Calendar,
  Weight,
  DollarSign,
  ArrowLeft,
  Edit,
  Eye
} from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

interface Truck {
  id: string
  truck_number: string
  driver_name: string
  driver_phone: string
  arrival_date: string
  departure_date?: string
  status: 'arrived' | 'unloading' | 'departed'
  notes?: string
  created_at: string
  updated_at: string
}

export default function TrucksPage() {
  const [trucks, setTrucks] = useState<Truck[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [newTruck, setNewTruck] = useState({
    truck_number: '',
    driver_name: '',
    driver_phone: '',
    arrival_date: new Date().toISOString().split('T')[0],
    notes: ''
  })
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/login')
        return
      }
      await loadTrucks()
    }
    checkAuth()
  }, [router])

  const loadTrucks = async () => {
    try {
      const { data, error } = await supabase
        .from('trucks')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setTrucks(data || [])
    } catch (error) {
      console.error('Error loading trucks:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddTruck = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const { data, error } = await supabase
        .from('trucks')
        .insert([{
          ...newTruck,
          status: 'arrived'
        }])
        .select()

      if (error) throw error

      setTrucks([data[0], ...trucks])
      setNewTruck({
        truck_number: '',
        driver_name: '',
        driver_phone: '',
        arrival_date: new Date().toISOString().split('T')[0],
        notes: ''
      })
      setShowAddForm(false)
    } catch (error) {
      console.error('Error adding truck:', error)
      alert('حدث خطأ في إضافة الجرار')
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'arrived': return 'وصل'
      case 'unloading': return 'يتم التفريغ'
      case 'departed': return 'غادر'
      default: return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'arrived': return 'bg-blue-100 text-blue-800'
      case 'unloading': return 'bg-yellow-100 text-yellow-800'
      case 'departed': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredTrucks = trucks.filter(truck =>
    truck.truck_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    truck.driver_name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/dashboard')}
                className="ml-4"
              >
                <ArrowLeft className="w-4 h-4 ml-2" />
                العودة
              </Button>
              <h1 className="text-xl font-semibold text-gray-900 flex items-center">
                <Truck className="w-6 h-6 ml-2" />
                إدارة الجرارات
              </h1>
            </div>
            <Button onClick={() => setShowAddForm(true)}>
              <Plus className="w-4 h-4 ml-2" />
              إضافة جرار جديد
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Search */}
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="البحث برقم الجرار أو اسم السائق..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </div>

          {/* Add Truck Form */}
          {showAddForm && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>إضافة جرار جديد</CardTitle>
                <CardDescription>
                  أدخل بيانات الجرار الجديد
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleAddTruck} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="truck_number">رقم الجرار</Label>
                      <Input
                        id="truck_number"
                        value={newTruck.truck_number}
                        onChange={(e) => setNewTruck({...newTruck, truck_number: e.target.value})}
                        placeholder="أدخل رقم الجرار"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="driver_name">اسم السائق</Label>
                      <Input
                        id="driver_name"
                        value={newTruck.driver_name}
                        onChange={(e) => setNewTruck({...newTruck, driver_name: e.target.value})}
                        placeholder="أدخل اسم السائق"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="driver_phone">رقم هاتف السائق</Label>
                      <Input
                        id="driver_phone"
                        value={newTruck.driver_phone}
                        onChange={(e) => setNewTruck({...newTruck, driver_phone: e.target.value})}
                        placeholder="أدخل رقم الهاتف"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="arrival_date">تاريخ الوصول</Label>
                      <Input
                        id="arrival_date"
                        type="date"
                        value={newTruck.arrival_date}
                        onChange={(e) => setNewTruck({...newTruck, arrival_date: e.target.value})}
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notes">ملاحظات</Label>
                    <Input
                      id="notes"
                      value={newTruck.notes}
                      onChange={(e) => setNewTruck({...newTruck, notes: e.target.value})}
                      placeholder="ملاحظات إضافية (اختياري)"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button type="submit">
                      حفظ الجرار
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                      إلغاء
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Trucks Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTrucks.map((truck) => (
              <Card key={truck.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">
                      جرار رقم {truck.truck_number}
                    </CardTitle>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(truck.status)}`}>
                      {getStatusText(truck.status)}
                    </span>
                  </div>
                  <CardDescription>
                    السائق: {truck.driver_name}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 ml-2 text-gray-500" />
                      <span>تاريخ الوصول: {formatDate(truck.arrival_date)}</span>
                    </div>
                    <div className="flex items-center">
                      <span>📞 {truck.driver_phone}</span>
                    </div>
                    {truck.notes && (
                      <div className="text-gray-600">
                        <strong>ملاحظات:</strong> {truck.notes}
                      </div>
                    )}
                  </div>
                  <div className="flex gap-2 mt-4">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => router.push(`/trucks/${truck.id}`)}
                    >
                      <Eye className="w-4 h-4 ml-1" />
                      عرض
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => router.push(`/trucks/${truck.id}/edit`)}
                    >
                      <Edit className="w-4 h-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredTrucks.length === 0 && (
            <div className="text-center py-12">
              <Truck className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                لا توجد جرارات
              </h3>
              <p className="text-gray-600 mb-4">
                {searchTerm ? 'لم يتم العثور على جرارات تطابق البحث' : 'لم يتم إضافة أي جرارات بعد'}
              </p>
              {!searchTerm && (
                <Button onClick={() => setShowAddForm(true)}>
                  <Plus className="w-4 h-4 ml-2" />
                  إضافة أول جرار
                </Button>
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
