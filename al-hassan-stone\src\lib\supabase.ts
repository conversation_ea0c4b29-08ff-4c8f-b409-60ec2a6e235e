import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface User {
  id: string
  email: string
  role: 'admin' | 'employee' | 'accountant'
  full_name: string
  created_at: string
  updated_at: string
}

export interface Truck {
  id: string
  truck_number: string
  supplier_name: string
  entry_date: string
  total_blocks: number
  total_weight: number
  price_per_ton: number
  total_cost: number
  invoice_image_url?: string
  created_at: string
  updated_at: string
}

export interface Block {
  id: string
  truck_id: string
  block_number: string
  length: number
  width: number
  height: number
  weight: number
  granite_type: string
  price: number
  created_at: string
  updated_at: string
}

export interface Slab {
  id: string
  block_id: string
  length: number
  height: number
  thickness: number
  quantity: number
  is_sold: boolean
  created_at: string
  updated_at: string
}

export interface Client {
  id: string
  name: string
  phone: string
  address: string
  created_at: string
  updated_at: string
}

export interface Sale {
  id: string
  client_id: string
  sale_date: string
  total_amount: number
  paid_amount: number
  remaining_amount: number
  created_at: string
  updated_at: string
}

export interface SaleItem {
  id: string
  sale_id: string
  slab_id: string
  length: number
  height: number
  thickness: number
  quantity: number
  price_per_sqm: number
  total_price: number
  created_at: string
  updated_at: string
}

export interface Expense {
  id: string
  type: 'fuel' | 'salary' | 'transport' | 'maintenance' | 'electricity' | 'other'
  amount: number
  date: string
  description: string
  invoice_image_url?: string
  created_at: string
  updated_at: string
}

export interface Inventory {
  id: string
  slab_id: string
  block_number: string
  length: number
  height: number
  thickness: number
  quantity: number
  granite_type: string
  created_at: string
  updated_at: string
}
