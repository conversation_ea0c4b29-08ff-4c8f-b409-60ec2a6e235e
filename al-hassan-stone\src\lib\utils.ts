import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Format currency in Arabic style
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('ar-EG', {
    style: 'currency',
    currency: 'EGP',
    minimumFractionDigits: 2,
  }).format(amount)
}

// Format date in Arabic
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(dateObj)
}

// Calculate area in square meters
export function calculateArea(length: number, height: number): number {
  return (length * height) / 10000 // Convert from cm² to m²
}

// Calculate volume in cubic meters
export function calculateVolume(length: number, width: number, height: number): number {
  return (length * width * height) / 1000000 // Convert from cm³ to m³
}

// Generate unique ID
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

// Validate Egyptian phone number
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^(\+20|0)?1[0-9]{9}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

// Format phone number
export function formatPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.startsWith('20')) {
    return `+${cleaned}`
  } else if (cleaned.startsWith('1')) {
    return `+20${cleaned}`
  } else if (cleaned.startsWith('01')) {
    return `+20${cleaned.substring(1)}`
  }
  return phone
}
